defmodule Drops.Operations.UnitOfWork do
  @moduledoc """
  UnitOfWork defines the processing pipeline for Operations.

  The UnitOfWork system provides a structured way to define and execute
  a series of processing steps in a specific order. Each step is defined
  as a tuple of `{module, function}` that will be called during processing.

  ## Default Pipeline

  The default pipeline consists of:

  - `:conform` - Validates input against the schema and transforms it
  - `:prepare` - Prepares the conformed parameters for validation
  - `:validate` - Validates the prepared parameters
  - `:execute` - Executes the operation with validated parameters

  ## Extension Override

  Extensions can override specific steps by providing their own implementations.
  For example, the Ecto extension overrides `:prepare` to return a changeset
  and `:validate` to work with changesets.

  ## Usage

      # Create a UnitOfWork for an operation module
      uow = UnitOfWork.new(MyOperation)

      # Process parameters through the pipeline
      case UnitOfWork.process(uow, params) do
        {:ok, result} -> # success
        {:error, error} -> # failure
      end

  """

  @type step :: :conform | :prepare | :validate | :execute
  @type step_definition :: {module(), atom()}
  @type t :: %__MODULE__{
          steps: %{step() => step_definition()},
          operation_module: module()
        }

  defstruct [:steps, :operation_module]

  @doc """
  Creates a new UnitOfWork for the given operation module.

  The UnitOfWork will be initialized with default steps that delegate
  to the operation module itself.

  ## Parameters

  - `operation_module` - The operation module to create a UnitOfWork for

  ## Returns

  Returns a new UnitOfWork struct.
  """
  @spec new(module()) :: t()
  def new(operation_module) do
    %__MODULE__{
      operation_module: operation_module,
      steps: %{
        conform: {operation_module, :conform},
        prepare: {operation_module, :prepare},
        validate: {operation_module, :validate},
        execute: {operation_module, :execute}
      }
    }
  end

  @doc """
  Overrides a specific step in the UnitOfWork.

  This allows extensions to replace default implementations with their own.

  ## Parameters

  - `uow` - The UnitOfWork to modify
  - `step` - The step to override (:conform, :prepare, :validate, or :execute)
  - `module` - The module that contains the override function
  - `function` - The function name to call

  ## Returns

  Returns the updated UnitOfWork.
  """
  @spec override_step(t(), step(), module(), atom()) :: t()
  def override_step(%__MODULE__{} = uow, step, module, function)
      when step in [:conform, :prepare, :validate, :execute] do
    put_in(uow.steps[step], {module, function})
  end

  @doc """
  Processes parameters through the UnitOfWork pipeline.

  This function executes the conform, prepare, and validate steps in order,
  but does NOT execute the :execute step. The execute step is handled
  separately by the Operations module.

  ## Parameters

  - `uow` - The UnitOfWork defining the pipeline
  - `params` - The initial parameters to process

  ## Returns

  Returns `{:ok, validated_params}` on success or `{:error, error}` on failure.
  """
  @spec process(t(), any()) :: {:ok, any()} | {:error, any()}
  def process(%__MODULE__{} = uow, params) do
    schema = uow.operation_module.schema()

    # Skip conform step if schema has no keys
    if length(schema.keys) == 0 do
      process_without_conform(uow, params)
    else
      process_with_conform(uow, params)
    end
  end

  # Private functions

  defp process_without_conform(uow, params) do
    with {:ok, prepared_params} <- call_step(uow, :prepare, params),
         {:ok, validated_params} <- call_step(uow, :validate, prepared_params) do
      {:ok, validated_params}
    end
  end

  defp process_with_conform(uow, params) do
    with {:ok, conformed_params} <- call_step(uow, :conform, params),
         {:ok, prepared_params} <- call_step(uow, :prepare, conformed_params),
         {:ok, validated_params} <- call_step(uow, :validate, prepared_params) do
      {:ok, validated_params}
    end
  end

  defp call_step(uow, step, params) do
    {module, function} = uow.steps[step]

    case step do
      :conform ->
        # conform returns {:ok, result} or {:error, errors}
        apply(module, function, [params])

      :prepare ->
        # prepare returns the prepared params directly
        # If the module is not the operation module, pass operation module as first arg
        result =
          if module == uow.operation_module do
            apply(module, function, [params])
          else
            apply(module, function, [uow.operation_module, params])
          end

        {:ok, result}

      :validate ->
        # validate returns the validated params directly
        # If the module is not the operation module, pass operation module as first arg
        result =
          if module == uow.operation_module do
            apply(module, function, [params])
          else
            apply(module, function, [uow.operation_module, params])
          end

        {:ok, result}
    end
  rescue
    error ->
      {:error, error}
  end
end
